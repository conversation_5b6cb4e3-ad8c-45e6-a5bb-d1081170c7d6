# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a Vue 3 + TypeScript + Vite project using Ant Design Vue for UI components. The application features a form management interface with file configuration capabilities.

## Development Commands
- `npm run dev` - Start development server (runs on port 9999, accessible from all interfaces)
- `npm run build` - Build for production (includes TypeScript compilation)
- `npm run preview` - Preview production build

## Architecture
- **Frontend Framework**: Vue 3 with Composition API and `<script setup>`
- **UI Library**: Ant Design Vue 4.x with full component imports
- **Build Tool**: Vite with TypeScript support
- **Package Manager**: pnpm (evidenced by pnpm-lock.yaml)

## Key Files
- `src/App.vue` - Main application component containing a comprehensive form with file name input, toggle switches, and dynamic tag management
- `src/main.ts` - Application entry point with Ant Design Vue global registration
- `vite.config.ts` - Custom server configuration (host: 0.0.0.0, port: 9999)

## Code Patterns
- Uses Vue 3 Composition API with reactive state management
- Implements form validation with Ant Design Vue's built-in validation
- Features dynamic component interaction (tag addition/removal, form state management)
- Follows TypeScript best practices with proper type annotations
- Uses scoped styling with deep selectors for component customization

## Current State
The main application is a form interface for file configuration management with:
- File name input with character limits and validation
- Enable/disable toggle switches
- Dynamic tag management system
- Form validation and submission handling
- Responsive design with mobile breakpoints