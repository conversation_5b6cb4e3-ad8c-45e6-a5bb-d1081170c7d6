<template>
  <div class="knowledge-base-layout">
    <!-- 左侧系统菜单 -->
    <div class="system-menu" :class="{ collapsed: menuCollapsed }">
      <div class="logo">
        <h2 v-if="!menuCollapsed">智能AI助手</h2>
        <h2 v-else class="logo-collapsed">AI</h2>
      </div>
      <div class="menu-toggle" @click="toggleMenu">
        <MenuFoldOutlined v-if="!menuCollapsed" />
        <MenuUnfoldOutlined v-else />
      </div>
      <a-menu
        v-model:selectedKeys="selectedMenuKeys"
        mode="inline"
        theme="dark"
        class="menu"
        :inline-collapsed="menuCollapsed"
      >
        <a-menu-item key="dashboard">
          <template #icon>
            <DashboardOutlined />
          </template>
          <span>仪表盘</span>
        </a-menu-item>
        <a-menu-item key="knowledge" class="active">
          <template #icon>
            <BookOutlined />
          </template>
          <span>知识库</span>
        </a-menu-item>
        <a-menu-item key="chat">
          <template #icon>
            <MessageOutlined />
          </template>
          <span>对话管理</span>
        </a-menu-item>
        <a-menu-item key="settings">
          <template #icon>
            <SettingOutlined />
          </template>
          <span>系统设置</span>
        </a-menu-item>
      </a-menu>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 知识库群组面板 -->
      <div class="group-panel">
        <div class="panel-header">
          <h3>知识库群组</h3>
          <a-button type="primary" size="small" @click="showCreateGroupModal">
            <PlusOutlined />
            新建群组
          </a-button>
        </div>

        <div class="group-list">
          <div
            v-for="group in knowledgeGroups"
            :key="group.id"
            :class="['group-item', { active: selectedGroupId === group.id }]"
            @click="selectGroup(group.id)"
          >
            <div class="group-info">
              <FolderOutlined class="group-icon" />
              <div class="group-details">
                <div class="group-name">{{ group.name }}</div>
                <div class="group-count">{{ group.count }} 个知识库</div>
              </div>
            </div>
            <a-dropdown :trigger="['click']">
              <EllipsisOutlined class="group-actions" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item key="edit" @click="editGroup(group)">
                    <EditOutlined />
                    编辑
                  </a-menu-item>
                  <a-menu-item key="delete" @click="deleteGroup(group.id)">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>

      <!-- 知识库列表 -->
      <div class="knowledge-list">
        <div class="list-header">
          <div class="header-left">
            <h2>知识库列表</h2>
            <span class="count-info">共 {{ filteredKnowledgeBases.length }} 个知识库</span>
          </div>
          <div class="header-right">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索知识库..."
              style="width: 300px; margin-right: 16px"
              @search="handleSearch"
            />
            <a-button type="primary" @click="showCreateKnowledgeModal">
              <PlusOutlined />
              创建知识库
            </a-button>
          </div>
        </div>

        <div class="list-content">
          <a-row :gutter="[16, 16]">
            <a-col
              v-for="kb in filteredKnowledgeBases"
              :key="kb.id"
              :xs="24" :sm="12" :md="8" :lg="6"
            >
              <a-card
                hoverable
                class="knowledge-card"
                @click="openKnowledge(kb)"
              >
                <template #cover>
                  <div class="card-cover">
                    <BookOutlined class="cover-icon" />
                  </div>
                </template>

                <template #actions>
                  <EditOutlined @click.stop="editKnowledge(kb)" />
                  <ShareAltOutlined @click.stop="shareKnowledge(kb)" />
                  <DeleteOutlined @click.stop="deleteKnowledge(kb.id)" />
                </template>

                <a-card-meta>
                  <template #title>
                    <div class="card-title">{{ kb.name }}</div>
                  </template>
                  <template #description>
                    <div class="card-description">{{ kb.description }}</div>
                  </template>
                </a-card-meta>

                <div class="card-footer">
                  <div class="card-stats">
                    <span class="stat-item">
                      <FileTextOutlined />
                      {{ kb.documentCount }} 文档
                    </span>
                    <span class="stat-item">
                      <ClockCircleOutlined />
                      {{ kb.updateTime }}
                    </span>
                  </div>
                  <div class="card-tags">
                    <a-tag
                      v-for="tag in kb.tags.slice(0, 2)"
                      :key="tag"
                      size="small"
                    >
                      {{ tag }}
                    </a-tag>
                    <span v-if="kb.tags.length > 2" class="more-tags">
                      +{{ kb.tags.length - 2 }}
                    </span>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>

          <!-- 空状态 -->
          <a-empty
            v-if="filteredKnowledgeBases.length === 0"
            description="暂无知识库"
            class="empty-state"
          >
            <a-button type="primary" @click="showCreateKnowledgeModal">
              创建第一个知识库
            </a-button>
          </a-empty>
        </div>
      </div>
    </div>

    <!-- 创建群组弹窗 -->
    <a-modal
      v-model:open="createGroupModalVisible"
      title="创建知识库群组"
      @ok="handleCreateGroup"
      @cancel="resetGroupForm"
    >
      <a-form
        ref="groupFormRef"
        :model="groupForm"
        layout="vertical"
      >
        <a-form-item
          label="群组名称"
          name="name"
          :rules="[{ required: true, message: '请输入群组名称' }]"
        >
          <a-input v-model:value="groupForm.name" placeholder="请输入群组名称" />
        </a-form-item>
        <a-form-item
          label="群组描述"
          name="description"
        >
          <a-textarea
            v-model:value="groupForm.description"
            placeholder="请输入群组描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建知识库弹窗 -->
    <a-modal
      v-model:open="createKnowledgeModalVisible"
      title="创建知识库"
      @ok="handleCreateKnowledge"
      @cancel="resetKnowledgeForm"
      width="600px"
    >
      <a-form
        ref="knowledgeFormRef"
        :model="knowledgeForm"
        layout="vertical"
      >
        <a-form-item
          label="知识库名称"
          name="name"
          :rules="[{ required: true, message: '请输入知识库名称' }]"
        >
          <a-input v-model:value="knowledgeForm.name" placeholder="请输入知识库名称" />
        </a-form-item>
        <a-form-item
          label="所属群组"
          name="groupId"
          :rules="[{ required: true, message: '请选择所属群组' }]"
        >
          <a-select v-model:value="knowledgeForm.groupId" placeholder="请选择所属群组">
            <a-select-option
              v-for="group in knowledgeGroups"
              :key="group.id"
              :value="group.id"
            >
              {{ group.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="知识库描述"
          name="description"
        >
          <a-textarea
            v-model:value="knowledgeForm.description"
            placeholder="请输入知识库描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item
          label="标签"
          name="tags"
        >
          <div class="tags-input">
            <a-tag
              v-for="(tag, index) in knowledgeForm.tags"
              :key="index"
              closable
              @close="removeKnowledgeTag(index)"
            >
              {{ tag }}
            </a-tag>
            <a-input
              v-if="newKnowledgeTagVisible"
              v-model:value="newKnowledgeTagValue"
              @pressEnter="addKnowledgeTag"
              @blur="addKnowledgeTag"
              style="width: 120px"
              size="small"
            />
            <a-tag
              v-else
              @click="showNewKnowledgeTagInput"
              style="background: #fff; border-style: dashed;"
            >
              <PlusOutlined />
              新标签
            </a-tag>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import {
  PlusOutlined,
  DashboardOutlined,
  BookOutlined,
  MessageOutlined,
  SettingOutlined,
  FolderOutlined,
  EllipsisOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

// 菜单选中状态
const selectedMenuKeys = ref(['knowledge'])

// 知识库群组数据
const knowledgeGroups = ref([
  { id: '1', name: '技术文档', description: '技术相关的知识库', count: 5 },
  { id: '2', name: '产品资料', description: '产品相关的知识库', count: 3 },
  { id: '3', name: '法律法规', description: '法律法规相关的知识库', count: 8 },
  { id: '4', name: '培训材料', description: '培训相关的知识库', count: 2 }
])

// 选中的群组
const selectedGroupId = ref('1')

// 知识库数据
const knowledgeBases = ref([
  {
    id: '1',
    name: 'Vue.js 开发指南',
    description: 'Vue.js 框架的完整开发指南和最佳实践',
    groupId: '1',
    documentCount: 25,
    updateTime: '2024-01-15',
    tags: ['Vue', 'JavaScript', '前端', '框架']
  },
  {
    id: '2',
    name: 'TypeScript 进阶教程',
    description: 'TypeScript 高级特性和实战应用',
    groupId: '1',
    documentCount: 18,
    updateTime: '2024-01-12',
    tags: ['TypeScript', 'JavaScript', '类型系统']
  },
  {
    id: '3',
    name: '产品需求文档',
    description: '产品功能需求和设计规范',
    groupId: '2',
    documentCount: 42,
    updateTime: '2024-01-10',
    tags: ['产品', '需求', '设计']
  },
  {
    id: '4',
    name: '政府采购法规',
    description: '政府采购相关法律法规汇总',
    groupId: '3',
    documentCount: 156,
    updateTime: '2024-01-08',
    tags: ['法规', '采购', '政府']
  },
  {
    id: '5',
    name: '员工培训手册',
    description: '新员工入职培训材料',
    groupId: '4',
    documentCount: 12,
    updateTime: '2024-01-05',
    tags: ['培训', '入职', '手册']
  }
])

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的知识库列表
const filteredKnowledgeBases = computed(() => {
  let filtered = knowledgeBases.value

  // 按群组过滤
  if (selectedGroupId.value) {
    filtered = filtered.filter(kb => kb.groupId === selectedGroupId.value)
  }

  // 按搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(kb =>
      kb.name.toLowerCase().includes(keyword) ||
      kb.description.toLowerCase().includes(keyword) ||
      kb.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

// 群组相关
const createGroupModalVisible = ref(false)
const groupFormRef = ref<FormInstance>()
const groupForm = reactive({
  name: '',
  description: ''
})

// 知识库相关
const createKnowledgeModalVisible = ref(false)
const knowledgeFormRef = ref<FormInstance>()
const knowledgeForm = reactive({
  name: '',
  groupId: '',
  description: '',
  tags: [] as string[]
})

// 知识库标签相关
const newKnowledgeTagVisible = ref(false)
const newKnowledgeTagValue = ref('')

// 选择群组
const selectGroup = (groupId: string) => {
  selectedGroupId.value = groupId
}

// 搜索处理
const handleSearch = (value: string) => {
  searchKeyword.value = value
}

// 显示创建群组弹窗
const showCreateGroupModal = () => {
  createGroupModalVisible.value = true
}

// 创建群组
const handleCreateGroup = async () => {
  try {
    await groupFormRef.value?.validate()

    const newGroup = {
      id: Date.now().toString(),
      name: groupForm.name,
      description: groupForm.description,
      count: 0
    }

    knowledgeGroups.value.push(newGroup)
    message.success('群组创建成功')
    createGroupModalVisible.value = false
    resetGroupForm()
  } catch (error) {
    console.error('群组创建失败:', error)
  }
}

// 重置群组表单
const resetGroupForm = () => {
  groupForm.name = ''
  groupForm.description = ''
  groupFormRef.value?.resetFields()
}

// 编辑群组
const editGroup = (group: any) => {
  message.info(`编辑群组: ${group.name}`)
}

// 删除群组
const deleteGroup = (groupId: string) => {
  const index = knowledgeGroups.value.findIndex(g => g.id === groupId)
  if (index > -1) {
    knowledgeGroups.value.splice(index, 1)
    message.success('群组删除成功')

    // 如果删除的是当前选中的群组，切换到第一个群组
    if (selectedGroupId.value === groupId && knowledgeGroups.value.length > 0) {
      selectedGroupId.value = knowledgeGroups.value[0].id
    }
  }
}

// 显示创建知识库弹窗
const showCreateKnowledgeModal = () => {
  knowledgeForm.groupId = selectedGroupId.value
  createKnowledgeModalVisible.value = true
}

// 创建知识库
const handleCreateKnowledge = async () => {
  try {
    await knowledgeFormRef.value?.validate()

    const newKnowledge = {
      id: Date.now().toString(),
      name: knowledgeForm.name,
      description: knowledgeForm.description,
      groupId: knowledgeForm.groupId,
      documentCount: 0,
      updateTime: new Date().toISOString().split('T')[0],
      tags: [...knowledgeForm.tags]
    }

    knowledgeBases.value.push(newKnowledge)

    // 更新群组计数
    const group = knowledgeGroups.value.find(g => g.id === knowledgeForm.groupId)
    if (group) {
      group.count++
    }

    message.success('知识库创建成功')
    createKnowledgeModalVisible.value = false
    resetKnowledgeForm()
  } catch (error) {
    console.error('知识库创建失败:', error)
  }
}

// 重置知识库表单
const resetKnowledgeForm = () => {
  knowledgeForm.name = ''
  knowledgeForm.groupId = ''
  knowledgeForm.description = ''
  knowledgeForm.tags = []
  knowledgeFormRef.value?.resetFields()
}

// 知识库标签管理
const removeKnowledgeTag = (index: number) => {
  knowledgeForm.tags.splice(index, 1)
}

const showNewKnowledgeTagInput = () => {
  newKnowledgeTagVisible.value = true
}

const addKnowledgeTag = () => {
  if (newKnowledgeTagValue.value && !knowledgeForm.tags.includes(newKnowledgeTagValue.value)) {
    knowledgeForm.tags.push(newKnowledgeTagValue.value)
  }
  newKnowledgeTagVisible.value = false
  newKnowledgeTagValue.value = ''
}

// 知识库操作
const openKnowledge = (kb: any) => {
  message.info(`打开知识库: ${kb.name}`)
}

const editKnowledge = (kb: any) => {
  message.info(`编辑知识库: ${kb.name}`)
}

const shareKnowledge = (kb: any) => {
  message.info(`分享知识库: ${kb.name}`)
}

const deleteKnowledge = (kbId: string) => {
  const index = knowledgeBases.value.findIndex(kb => kb.id === kbId)
  if (index > -1) {
    const kb = knowledgeBases.value[index]
    knowledgeBases.value.splice(index, 1)

    // 更新群组计数
    const group = knowledgeGroups.value.find(g => g.id === kb.groupId)
    if (group) {
      group.count--
    }

    message.success('知识库删除成功')
  }
}

</script>

<style scoped>
/* 整体布局 */
.knowledge-base-layout {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

/* 左侧系统菜单 */
.system-menu {
  width: 240px;
  background: #001529;
  display: flex;
  flex-direction: column;
}

.logo {
  padding: 24px 16px;
  text-align: center;
  border-bottom: 1px solid #303030;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.menu {
  flex: 1;
  border-right: none;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 知识库群组面板 */
.group-panel {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.group-item:hover {
  background: #f5f5f5;
}

.group-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.group-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.group-icon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 12px;
}

.group-details {
  flex: 1;
}

.group-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.group-count {
  font-size: 12px;
  color: #8c8c8c;
}

.group-actions {
  color: #8c8c8c;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.group-actions:hover {
  background: #f0f0f0;
  color: #262626;
}

/* 知识库列表 */
.knowledge-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: baseline;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.count-info {
  font-size: 14px;
  color: #8c8c8c;
}

.header-right {
  display: flex;
  align-items: center;
}

.list-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 知识库卡片 */
.knowledge-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.knowledge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-cover {
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-icon {
  font-size: 48px;
  color: #fff;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-description {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.5;
  height: 42px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.card-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #8c8c8c;
  gap: 4px;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-tags {
  font-size: 12px;
  color: #8c8c8c;
}

/* 空状态 */
.empty-state {
  margin-top: 80px;
}

/* 标签输入 */
.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .group-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .knowledge-base-layout {
    flex-direction: column;
  }

  .system-menu {
    width: 100%;
    height: auto;
  }

  .main-content {
    flex-direction: column;
  }

  .group-panel {
    width: 100%;
    height: 200px;
  }

  .knowledge-list {
    margin: 8px;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    justify-content: space-between;
  }
}

/* 深度样式覆盖 */
:deep(.ant-card-actions) {
  background: #fafafa;
}

:deep(.ant-card-actions > li) {
  margin: 8px 0;
}

:deep(.ant-card-actions > li > span) {
  color: #8c8c8c;
  font-size: 16px;
  transition: all 0.2s;
}

:deep(.ant-card-actions > li > span:hover) {
  color: #1890ff;
}

:deep(.ant-menu-item.active) {
  background-color: #1890ff !important;
}
</style>
